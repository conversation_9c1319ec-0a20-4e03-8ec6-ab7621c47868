from enum import Enum
import os
import logging
import asyncio

from typing import Any, List, Optional, Iterator, AsyncIterator
from litellm import completion, embedding, AuthenticationError, RateLimitError, APIError
from python.helpers import dotenv
from python.helpers.dotenv import load_dotenv
from python.helpers.rate_limiter import RateLimiter
from pydantic import Field

from langchain_core.language_models.chat_models import SimpleChatModel, ChatGenerationChunk
from langchain_core.callbacks.manager import (
    CallbackManagerForLLMRun,
    AsyncCallbackManagerForLLMRun,
)
from langchain_core.messages import BaseMessage, AIMessageChunk, ToolMessage, AIMessage
from langchain.embeddings.base import Embeddings

load_dotenv()

class ModelType(Enum):
    CHAT = "Chat"
    EMBEDDING = "Embedding"

class ModelProvider(Enum):
    ANTHROPIC = "Anthropic"
    CHUTES = "Chutes"
    DEEPSEEK = "DeepSeek"
    GOOGLE = "Google"
    GROQ = "Groq"
    HUGGINGFACE = "HuggingFace"
    LMSTUDIO = "LM Studio"
    MISTRALAI = "Mistral AI"
    OLLAMA = "Ollama"
    OPENAI = "OpenAI"
    OPENAI_AZURE = "OpenAI Azure"
    OPENROUTER = "OpenRouter"
    SAMBANOVA = "Sambanova"
    OTHER = "Other"

rate_limiters: dict[str, RateLimiter] = {}

LITELLM_PROVIDER_MAP = {
    "OPENAI": "openai",
    "ANTHROPIC": "anthropic",
    "GROQ": "groq",
    "GOOGLE": "gemini",  # Fix: Use "gemini" for LiteLLM provider (Google AI Studio)
    "MISTRALAI": "mistral",
    "OLLAMA": "ollama",
    "HUGGINGFACE": "huggingface",
    "OPENAI_AZURE": "azure",
    "DEEPSEEK": "deepseek",
    "SAMBANOVA": "sambanova",
}

def configure_litellm_environment():
    env_mappings = {
        "API_KEY_OPENAI": "OPENAI_API_KEY",
        "API_KEY_ANTHROPIC": "ANTHROPIC_API_KEY",
        "API_KEY_GROQ": "GROQ_API_KEY",
        "API_KEY_GOOGLE": "GEMINI_API_KEY",  # Fix: LiteLLM expects GEMINI_API_KEY for Google/Gemini
        "API_KEY_MISTRAL": "MISTRAL_API_KEY",
        "API_KEY_MISTRALAI": "MISTRAL_API_KEY",  # Support both variants
        "API_KEY_OLLAMA": "OLLAMA_API_KEY",
        "API_KEY_HUGGINGFACE": "HUGGINGFACE_API_KEY",
        "API_KEY_OPENAI_AZURE": "AZURE_API_KEY",
        "API_KEY_DEEPSEEK": "DEEPSEEK_API_KEY",
        "API_KEY_SAMBANOVA": "SAMBANOVA_API_KEY",
    }
    for a0, llm in env_mappings.items():
        val = dotenv.get_dotenv_value(a0)
        if val and not os.getenv(llm):
            os.environ[llm] = val


def get_api_key(service: str) -> str:
    return (
        dotenv.get_dotenv_value(f"API_KEY_{service.upper()}")
        or dotenv.get_dotenv_value(f"{service.upper()}_API_KEY")
        or dotenv.get_dotenv_value(f"{service.upper()}_API_TOKEN")
        or "None"
    )

def get_rate_limiter(
    provider: ModelProvider, name: str, requests: int, input: int, output: int
) -> RateLimiter:
    key = f"{provider.name}\{name}"
    rate_limiters[key] = limiter = rate_limiters.get(key, RateLimiter(seconds=60))
    limiter.limits["requests"] = requests or 0
    limiter.limits["input"] = input or 0
    limiter.limits["output"] = output or 0
    return limiter

def parse_chunk(chunk: Any):
    if isinstance(chunk, str):
        content = chunk
    elif hasattr(chunk, "content"):
        content = str(chunk.content)
    else:
        content = str(chunk)
    return content

def convert_kwargs_types(kwargs: dict[str, Any]) -> dict[str, Any]:
    """Convert string values to appropriate types for LiteLLM parameters."""
    converted = kwargs.copy()

    # Parameters that should be floats
    float_params = ["temperature", "top_p", "frequency_penalty", "presence_penalty"]
    for param in float_params:
        if param in converted and isinstance(converted[param], str):
            try:
                converted[param] = float(converted[param])
            except ValueError:
                pass  # Keep as string if conversion fails

    # Parameters that should be integers
    int_params = ["max_tokens", "n", "seed"]
    for param in int_params:
        if param in converted and isinstance(converted[param], str):
            try:
                converted[param] = int(converted[param])
            except ValueError:
                pass  # Keep as string if conversion fails

    return converted

def _handle_litellm_exception(e: Exception):
    """Raise a generic exception from a LiteLLM exception, with logging."""
    if isinstance(e, AuthenticationError):
        logging.error(f"LiteLLM Authentication failed: {e}")
        # Check for specific Google/Gemini authentication issues
        if "google" in str(e).lower() or "gemini" in str(e).lower():
            raise Exception("Google/Gemini API key authentication failed. Please verify your Google API key is correct and has the necessary permissions for the Gemini API.") from e
        else:
            raise Exception("API key authentication failed for provider. Please check your API key configuration.") from e
    elif isinstance(e, RateLimitError):
        logging.warning(f"LiteLLM Rate limit exceeded: {e}")
        raise Exception("Rate limit exceeded, please try again later.") from e
    elif isinstance(e, APIError):
        logging.error(f"LiteLLM API error: {e}")
        # Check for specific provider not found error
        if "LLM Provider NOT provided" in str(e):
            raise Exception(f"LiteLLM provider not recognized. This usually means the model format is incorrect or the provider is not supported. Error: {e}") from e
        # Check for Google/Gemini specific errors
        elif "google" in str(e).lower() or "gemini" in str(e).lower():
            raise Exception(f"Google/Gemini API error. Please check your API key and ensure the Gemini API is enabled in your Google Cloud Console. Error: {e}") from e
        else:
            raise Exception(f"Provider API error: {e}") from e
    else:
        logging.error(f"An unexpected LiteLLM error occurred: {e}")
        # Check for specific provider not found error in generic exceptions
        if "LLM Provider NOT provided" in str(e):
            raise Exception(f"LiteLLM provider not recognized. This usually means the model format is incorrect or the provider is not supported. Error: {e}") from e
        else:
            raise e


class LiteLLMChatWrapper(SimpleChatModel):
    model: str = Field(...)
    kwargs: dict[str, Any] = Field(default_factory=dict)

    def __init__(self, model: str, provider: str, **kwargs: Any):
        # Prevent double-prefixing: if model already contains a slash, don't add provider prefix
        if "/" in model and provider != "openai":
            formatted_model = model
            logging.warning(f"Model '{model}' already contains provider prefix, not adding '{provider}/' prefix")
        else:
            formatted_model = f"{provider}/{model}" if provider != "openai" else model

        converted_kwargs = convert_kwargs_types(kwargs)

        # Debug logging to help diagnose model format issues
        logging.debug(f"LiteLLMChatWrapper initialized with:")
        logging.debug(f"  Original model: {model}")
        logging.debug(f"  Provider: {provider}")
        logging.debug(f"  Formatted model: {formatted_model}")
        logging.debug(f"  Kwargs: {converted_kwargs}")

        super().__init__(model=formatted_model, kwargs=converted_kwargs)

    @property
    def _llm_type(self) -> str:
        return "litellm-chat"

    def _convert_messages(self, messages: List[BaseMessage]) -> List[dict]:
        result = []
        for m in messages:
            if m.type == "human":
                role = "user"
            elif m.type == "ai":
                role = "assistant"
            else:
                role = m.type # system, function, tool
            result.append({"role": role, "content": m.content})
        return result

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        msgs = self._convert_messages(messages)
        combined_kwargs = convert_kwargs_types({**self.kwargs, **kwargs})
        try:
            # Debug logging to help diagnose model format issues
            logging.debug(f"LiteLLM calling completion with model: {self.model}")
            logging.debug(f"LiteLLM kwargs: {combined_kwargs}")

            resp = completion(
                model=self.model, messages=msgs, stop=stop, **combined_kwargs
            )
            delta = resp["choices"][0].get("message")
            content = delta.get("content") if isinstance(delta, dict) else getattr(delta, "content", "")
            return content or ""
        except (AuthenticationError, RateLimitError, APIError) as e:
            _handle_litellm_exception(e)

    def _stream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        msgs = self._convert_messages(messages)
        combined_kwargs = convert_kwargs_types({**self.kwargs, **kwargs})
        try:
            # Debug logging to help diagnose model format issues
            logging.debug(f"LiteLLM streaming with model: {self.model}")
            logging.debug(f"LiteLLM kwargs: {combined_kwargs}")

            for chunk in completion(
                model=self.model,
                messages=msgs,
                stream=True,
                stop=stop,
                **combined_kwargs,
            ):
                delta = chunk["choices"][0].get("delta", {})
                content = delta.get("content", "") if isinstance(delta, dict) else getattr(delta, "content", "")
                if content is None:
                    content = ""
                yield ChatGenerationChunk(message=AIMessageChunk(content=content))
        except (AuthenticationError, RateLimitError, APIError) as e:
            _handle_litellm_exception(e)

    async def _astream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        msgs = self._convert_messages(messages)

        # Use the synchronous completion in an async wrapper since acompletion streaming has issues
        def sync_stream():
            combined_kwargs = convert_kwargs_types({**self.kwargs, **kwargs})
            try:
                # Debug logging to help diagnose model format issues
                logging.debug(f"LiteLLM async streaming with model: {self.model}")
                logging.debug(f"LiteLLM kwargs: {combined_kwargs}")

                for chunk in completion(
                    model=self.model,
                    messages=msgs,
                    stream=True,
                    stop=stop,
                    **combined_kwargs,
                ):
                    delta = chunk["choices"][0].get("delta", {})
                    content = delta.get("content", "") if isinstance(delta, dict) else getattr(delta, "content", "")
                    if content is None:
                        content = ""
                    yield ChatGenerationChunk(message=AIMessageChunk(content=content))
            except (AuthenticationError, RateLimitError, APIError) as e:
                _handle_litellm_exception(e)

        for chunk in sync_stream():
            yield chunk
            await asyncio.sleep(0)

class LiteLLMEmbeddingWrapper(Embeddings):
    def __init__(self, model: str, provider: str, **kwargs: Any):
        self.original_model = model
        self.provider = provider

        # Prevent double-prefixing: if model already contains a slash, don't add provider prefix
        if "/" in model and provider != "openai":
            self.model = model
            logging.warning(f"Embedding model '{model}' already contains provider prefix, not adding '{provider}/' prefix")
        else:
            self.model = f"{provider}/{model}" if provider != "openai" else model

        self.kwargs = kwargs
        self._sentence_transformer = None

        self._use_local = (
            provider.lower() == "huggingface" and
            ("sentence-transformers/" in model or model.startswith("all-"))
        )

        if self._use_local:
            try:
                from sentence_transformers import SentenceTransformer
                self._sentence_transformer = SentenceTransformer(model)
                logging.info(f"✅ Loaded local sentence-transformers model: {model}")
            except ImportError:
                logging.warning("⚠️ sentence-transformers not installed. Use: pip install sentence-transformers")
                logging.warning("Falling back to LiteLLM (requires HuggingFace API key)")
                self._use_local = False
            except Exception as e:
                logging.warning(f"⚠️ Failed to load local model {model}: {e}")
                logging.warning("Falling back to LiteLLM (requires HuggingFace API key)")
                self._use_local = False

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        if self._use_local and self._sentence_transformer:
            embeddings = self._sentence_transformer.encode(texts, convert_to_tensor=False)
            return embeddings.tolist() if hasattr(embeddings, 'tolist') else embeddings
        else:
            try:
                resp = embedding(model=self.model, input=texts, **self.kwargs)
                return [
                    item.get("embedding") if isinstance(item, dict) else item.embedding
                    for item in resp.data
                ]
            except (AuthenticationError, RateLimitError, APIError) as e:
                _handle_litellm_exception(e)

    def embed_query(self, text: str) -> List[float]:
        if self._use_local and self._sentence_transformer:
            embedding_result = self._sentence_transformer.encode([text], convert_to_tensor=False)
            return embedding_result[0].tolist() if hasattr(embedding_result[0], 'tolist') else embedding_result[0]
        else:
            try:
                resp = embedding(model=self.model, input=[text], **self.kwargs)
                item = resp.data[0]
                return item.get("embedding") if isinstance(item, dict) else item.embedding
            except (AuthenticationError, RateLimitError, APIError) as e:
                _handle_litellm_exception(e)


class BrowserCompatibleChatWrapper(LiteLLMChatWrapper):
    """
    A wrapper that makes LiteLLM models compatible with browser-use by filtering
    problematic message sequences that Gemini doesn't support.
    """

    def _filter_messages_for_gemini(self, messages: List[BaseMessage]) -> List[BaseMessage]:
        """
        Filter messages to avoid Gemini's strict tool message requirements.
        Removes tool messages that don't have preceding tool_calls.
        """
        filtered_messages = []
        last_message_had_tool_calls = False

        for message in messages:
            # Check if this is a tool message
            if hasattr(message, 'type') and message.type == 'tool':
                # Only include tool messages if the previous message had tool_calls
                if last_message_had_tool_calls:
                    filtered_messages.append(message)
                else:
                    # Skip this tool message and log a warning
                    logging.warning(f"Skipping tool message without preceding tool_calls: {message}")
                    continue
            else:
                filtered_messages.append(message)

            # Check if this message has tool_calls for the next iteration
            last_message_had_tool_calls = (
                hasattr(message, 'additional_kwargs') and
                'tool_calls' in message.additional_kwargs
            ) or (
                hasattr(message, 'tool_calls') and
                message.tool_calls
            )

        return filtered_messages

    def _call(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        # Filter messages for Gemini compatibility
        filtered_messages = self._filter_messages_for_gemini(messages)
        return super()._call(filtered_messages, stop, run_manager, **kwargs)

    def _stream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        # Filter messages for Gemini compatibility
        filtered_messages = self._filter_messages_for_gemini(messages)
        return super()._stream(filtered_messages, stop, run_manager, **kwargs)

    async def _astream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        # Filter messages for Gemini compatibility
        filtered_messages = self._filter_messages_for_gemini(messages)
        async for chunk in super()._astream(filtered_messages, stop, run_manager, **kwargs):
            yield chunk


def get_litellm_chat(model_name: str, provider: str, **kwargs: Any):
    configure_litellm_environment()
    api_key = kwargs.pop("api_key", None) or get_api_key(provider)
    base_url = kwargs.pop("base_url", None) or dotenv.get_dotenv_value(f"{provider.upper()}_BASE_URL")

    # Debug logging for API key configuration
    logging.debug(f"Configuring LiteLLM chat for provider: {provider}")
    logging.debug(f"Model: {model_name}")
    logging.debug(f"API key present: {'Yes' if api_key and api_key != 'None' else 'No'}")
    logging.debug(f"Base URL: {base_url if base_url else 'Default'}")

    if base_url:
        kwargs["base_url"] = base_url
    if api_key and api_key != "None":  # Fix: Don't pass "None" string as API key
        kwargs["api_key"] = api_key
    elif provider.lower() not in ["ollama", "lmstudio"]:  # These providers don't require API keys
        logging.warning(f"No API key found for provider: {provider}. This may cause authentication errors.")

    return LiteLLMChatWrapper(model=model_name, provider=provider, **kwargs)

def get_browser_compatible_chat(model_name: str, provider: str, **kwargs: Any):
    """
    Get a browser-compatible chat model that works well with browser-use.
    For Gemini models, this returns a wrapper that filters problematic message sequences.
    """
    configure_litellm_environment()
    api_key = kwargs.pop("api_key", None) or get_api_key(provider)
    base_url = kwargs.pop("base_url", None) or dotenv.get_dotenv_value(f"{provider.upper()}_BASE_URL")

    # Debug logging for API key configuration
    logging.debug(f"Configuring browser-compatible LiteLLM chat for provider: {provider}")
    logging.debug(f"Model: {model_name}")
    logging.debug(f"API key present: {'Yes' if api_key and api_key != 'None' else 'No'}")
    logging.debug(f"Base URL: {base_url if base_url else 'Default'}")

    if base_url:
        kwargs["base_url"] = base_url
    if api_key and api_key != "None":
        kwargs["api_key"] = api_key
    elif provider.lower() not in ["ollama", "lmstudio"]:
        logging.warning(f"No API key found for provider: {provider}. This may cause authentication errors.")

    # Use browser-compatible wrapper for Gemini models
    if provider.lower() == "gemini":
        return BrowserCompatibleChatWrapper(model=model_name, provider=provider, **kwargs)
    else:
        return LiteLLMChatWrapper(model=model_name, provider=provider, **kwargs)

def get_litellm_embedding(model_name: str, provider: str, **kwargs: Any):
    configure_litellm_environment()
    api_key = kwargs.pop("api_key", None) or get_api_key(provider)
    base_url = kwargs.pop("base_url", None) or dotenv.get_dotenv_value(f"{provider.upper()}_BASE_URL")

    # Debug logging for API key configuration
    logging.debug(f"Configuring LiteLLM embedding for provider: {provider}")
    logging.debug(f"Model: {model_name}")
    logging.debug(f"API key present: {'Yes' if api_key and api_key != 'None' else 'No'}")
    logging.debug(f"Base URL: {base_url if base_url else 'Default'}")

    if base_url:
        # For embeddings, litellm often uses api_base
        kwargs["api_base"] = base_url
    if api_key and api_key != "None":  # Fix: Don't pass "None" string as API key
        kwargs["api_key"] = api_key
    elif provider.lower() not in ["ollama", "lmstudio", "huggingface"]:  # These providers may not require API keys
        logging.warning(f"No API key found for embedding provider: {provider}. This may cause authentication errors.")

    return LiteLLMEmbeddingWrapper(model=model_name, provider=provider, **kwargs)

def get_model(type: ModelType, provider: ModelProvider, name: str, **kwargs: Any):
    provider_name = LITELLM_PROVIDER_MAP.get(provider.name, provider.name.lower())
    if type == ModelType.CHAT:
        return get_litellm_chat(name, provider_name, **kwargs)
    elif type == ModelType.EMBEDDING:
        return get_litellm_embedding(name, provider_name, **kwargs)
    else:
        raise ValueError(f"Unsupported model type: {type}")

def get_browser_compatible_chat(chat):
    return chat


def get_browser_compatible_chat(*args, **kwargs):
    """Stub for browser_agent compatibility"""
    return None

